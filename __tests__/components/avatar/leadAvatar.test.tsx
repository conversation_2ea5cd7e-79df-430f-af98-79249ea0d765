import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import "@testing-library/jest-dom/vitest";
import { LeadAvatar } from "../../../src/components/avatar/leadAvatar";

describe("LeadAvatar Component", () => {
  const mockProps = {
    image: "https://img.daisyui.com/images/profile/demo/<EMAIL>",
    leadStatus: "active" as const,
    onLeadStatusChange: vi.fn(),
  };

  it("should render avatar with correct ring color for active status", () => {
    const { container } = render(<LeadAvatar {...mockProps} />);
    const avatar = container.querySelector(".ring-error");
    expect(avatar).toBeInTheDocument();
  });

  it("should render avatar with correct ring color for completed status", () => {
    const { container } = render(<LeadAvatar {...mockProps} leadStatus="completed" />);
    const avatar = container.querySelector(".ring-primary");
    expect(avatar).toBeInTheDocument();
  });

  it("should render avatar with correct ring color for draft status", () => {
    const { container } = render(<LeadAvatar {...mockProps} leadStatus="draft" />);
    const avatar = container.querySelector(".ring-warning-content");
    expect(avatar).toBeInTheDocument();
  });

  it("should render avatar with correct ring color for suspended status", () => {
    const { container } = render(<LeadAvatar {...mockProps} leadStatus="suspended" />);
    const avatar = container.querySelector(".ring-neutral");
    expect(avatar).toBeInTheDocument();
  });

  it("should render select with current leadStatus value", () => {
    render(<LeadAvatar {...mockProps} />);
    const selectButton = screen.getByRole("button");
    fireEvent.click(selectButton);

    // Check if the active status options are present (both selected and in dropdown)
    const activeOptions = screen.getAllByText("ACTIVE");
    expect(activeOptions).toHaveLength(2); // One in selected value, one in dropdown
  });

  it("should call onLeadStatusChange when status is changed", () => {
    const onLeadStatusChange = vi.fn();
    render(<LeadAvatar {...mockProps} onLeadStatusChange={onLeadStatusChange} />);
    
    const selectButton = screen.getByRole("button");
    fireEvent.click(selectButton);
    
    const completedOption = screen.getByText("COMPLETED");
    fireEvent.click(completedOption);
    
    expect(onLeadStatusChange).toHaveBeenCalledWith("completed");
  });

  it("should not call onLeadStatusChange when prop is not provided", () => {
    const { container } = render(<LeadAvatar image={mockProps.image} leadStatus={mockProps.leadStatus} />);
    const selectButton = screen.getByRole("button");
    fireEvent.click(selectButton);
    
    const completedOption = screen.getByText("COMPLETED");
    fireEvent.click(completedOption);
    
    // Should not throw error when onLeadStatusChange is undefined
    expect(container).toBeInTheDocument();
  });
});
