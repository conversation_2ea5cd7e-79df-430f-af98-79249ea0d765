import { LEAD_STATUS_OPTIONS, LeadStatusBadge, type LeadStatusType } from "@components/badge";
import { Avatar, Select } from "@components/common";
import { cn } from "@utils/cn";

interface LeadAvatarProps {
  image: string;
  leadStatus: LeadStatusType;
}

const SelectOptions = (["active", "completed", "draft", "suspended"] as LeadStatusType[]).map(
  (type) => ({
    label: <LeadStatusBadge type={type} />,
    value: type,
  }),
);

export const LeadAvatar = ({ image, leadStatus }: LeadAvatarProps) => {
  return (
    <div className="relative flex items-center gap-1">
      <Avatar
        className={cn("w-10 ring-2 ring-offset-2 ring-offset-base-100", {
          "ring-error": leadStatus === "active",
          "ring-neutral": leadStatus === "suspended",
          "ring-primary": leadStatus === "completed",
          "ring-warning-content": leadStatus === "draft",
        })}
        image={image}
      />
      <Select
        options={SelectOptions}
        size="sm"
        variant="popup"
        value=""
        onChange={(value) => fieldApi.handleChange(value)}
        className={cn("flex-1")}
        placeholder={field.placeholder}
      />
    </div>
  );
};
