import { LEAD_STATUS_OPTIONS, type LeadStatusType } from "@components/badge";
import { Avatar, Select } from "@components/common";
import { cn } from "@utils/cn";

interface LeadAvatarProps {
  image: string;
  leadStatus: LeadStatusType;
  onLeadStatusChange?: (status: LeadStatusType) => void;
}

export const LeadAvatar = ({ image, leadStatus, onLeadStatusChange }: LeadAvatarProps) => {
  return (
    <div className="relative flex items-center gap-1">
      <Avatar
        className={cn("w-10 ring-2 ring-offset-2 ring-offset-base-100", {
          "ring-error": leadStatus === "active",
          "ring-neutral": leadStatus === "suspended",
          "ring-primary": leadStatus === "completed",
          "ring-warning-content": leadStatus === "draft",
        })}
        image={image}
      />
      <Select
        options={LEAD_STATUS_OPTIONS}
        size="sm"
        variant="popup"
        value={leadStatus}
        onChange={(value) => onLeadStatusChange?.(value as LeadStatusType)}
        className={cn("flex-1")}
        placeholder="Select status"
      />
    </div>
  );
};
